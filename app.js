const utils = require('./utils/util');
const user = require('./request/user')

App({
    onLaunch() {
        //检查登陆状态
        this.checkGlobalLoginStatus();
    },

    // 全局登录状态检查
    checkGlobalLoginStatus() {
        console.log('App启动 - 开始检查全局登录状态');

        // 检查微信登录session是否过期
        wx.checkSession({
            success: () => {
                console.log('微信session有效');
                // session有效，检查本地存储的用户信息
                this.checkLocalUserInfo();
            },
            fail: () => {
                console.log('微信session已过期，清理本地数据');
                // session过期，清理相关存储
                this.clearExpiredData();
            }
        });
    },

    // 检查本地用户信息
    checkLocalUserInfo() {
        try {
            const userInfo = wx.getStorageSync('userInfo');
            const openId = wx.getStorageSync('OpenId');

            console.log('=== App启动 - 本地用户信息检查 ===');
            console.log('存储状态:', {
                hasUserInfo: !!userInfo,
                hasOpenId: !!openId,
                userInfo: userInfo
            });

            if (userInfo && (userInfo.PersonId || userInfo.userId)) {
                console.log('✅ 发现有效的本地用户信息，设置全局登录状态为已登录');
                // 用户信息完整，设置全局登录状态
                this.globalData.isLoggedIn = true;
                this.globalData.userInfo = userInfo;
                console.log('全局状态已更新:', {
                    isLoggedIn: this.globalData.isLoggedIn,
                    userInfo: this.globalData.userInfo
                });
            } else if (openId) {
                console.log('⚠️ 有OpenId但无完整用户信息，需要重新获取用户信息');
                // 有openId但用户信息不完整，可能需要重新登录
                this.globalData.isLoggedIn = false;
            } else {
                console.log('❌ 无有效登录信息');
                this.globalData.isLoggedIn = false;
            }
        } catch (error) {
            console.error('检查本地用户信息失败:', error);
            this.globalData.isLoggedIn = false;
        }
    },

    // 清理过期数据
    clearExpiredData() {
        try {
            wx.removeStorageSync('OpenId');
            // 注意：这里不清理userInfo，因为可能在其他地方还需要
            console.log('已清理过期的OpenId');
        } catch (error) {
            console.error('清理过期数据失败:', error);
        }
        this.globalData.isLoggedIn = false;
    },

    globalData: {
        // 现场信息反馈功能的基础URL
        feedbackBaseUrl: 'http://192.168.16.94:8081',
        // 全局登录状态
        isLoggedIn: false,
        userInfo: null
    },

    /**
     * 将字符串转换为ArrayBuffer（小程序兼容版本）
     * @param {string} str 要转换的字符串
     * @returns {ArrayBuffer} 转换后的ArrayBuffer
     */
    stringToArrayBuffer(str) {
        const buf = new ArrayBuffer(str.length * 2); // 2 bytes for each char
        const bufView = new Uint16Array(buf);
        for (let i = 0, strLen = str.length; i < strLen; i++) {
            bufView[i] = str.charCodeAt(i);
        }
        return buf;
    },

    /**
     * 简单的base64编码（小程序兼容版本）
     * @param {string} str 要编码的字符串
     * @returns {string} base64编码后的字符串
     */
    base64Encode(str) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
        let result = '';
        let i = 0;

        while (i < str.length) {
            const a = str.charCodeAt(i++);
            const b = i < str.length ? str.charCodeAt(i++) : 0;
            const c = i < str.length ? str.charCodeAt(i++) : 0;

            const bitmap = (a << 16) | (b << 8) | c;

            result += chars.charAt((bitmap >> 18) & 63);
            result += chars.charAt((bitmap >> 12) & 63);
            result += i - 2 < str.length ? chars.charAt((bitmap >> 6) & 63) : '=';
            result += i - 1 < str.length ? chars.charAt(bitmap & 63) : '=';
        }

        return result;
    },

    /**
     * 现场信息反馈功能的网络请求方法
     * 用于fbindex页面及相关功能的API调用
     */
    request(options) {
        return new Promise((resolve, reject) => {
            // 从车辆维保小程序的存储中获取用户信息
            const userInfo = wx.getStorageSync("userInfo");
            const currentCompany = wx.getStorageSync("currentCompany");

            // 默认请求配置
            const defaultOptions = {
                method: 'GET',
                timeout: 30000, // 增加到30秒
                header: {
                    "Content-Type": "application/json",
                },
            };

            // 如果有用户信息，生成token并添加认证信息
            if (userInfo && (userInfo.PersonId || userInfo.userId)) {
                try {
                    // 生成临时token（与后端认证中间件兼容的格式）
                    const userId = userInfo.PersonId || userInfo.userId;
                    const username = userInfo.PersonName || userInfo.UserName || '用户';
                    const tokenData = `${userId}:${username}:${Date.now()}`;
                    const token = this.base64Encode(tokenData);

                    // 添加token到header
                    defaultOptions.header["authorization"] = token;

                    // 使用encodeURIComponent对包含中文的JSON字符串进行编码
                    const userInfoJson = JSON.stringify({
                        PersonId: userInfo.PersonId || userInfo.userId,
                        PersonName: userInfo.PersonName || userInfo.UserName,
                        Phone: userInfo.Phone
                    });
                    defaultOptions.header["X-User-Info"] = encodeURIComponent(userInfoJson);

                    console.log('已生成认证token:', {
                        userId: userId,
                        username: username,
                        tokenLength: token.length
                    });
                } catch (error) {
                    console.warn('生成认证信息失败:', error);
                }
            } else {
                console.warn('用户信息不完整，无法生成认证token:', userInfo);
            }

            // 如果有当前公司信息，添加到header中
            if (currentCompany) {
                try {
                    const companyJson = JSON.stringify(currentCompany);
                    defaultOptions.header["X-Current-Company"] = encodeURIComponent(companyJson);
                } catch (error) {
                    console.warn('序列化公司信息失败:', error);
                }
            }

            // 合并默认配置和传入的配置
            const requestOptions = {
                ...defaultOptions,
                ...options,
                header: {
                    ...defaultOptions.header,
                    ...options.header,
                },
                // 处理URL：如果是完整URL则直接使用，否则拼接baseUrl
                url: options.url.startsWith("http")
                    ? options.url
                    : `${this.globalData.feedbackBaseUrl}${options.url}`,
                success: (res) => {
                    resolve(res);
                },
                fail: (error) => {
                    console.error('请求失败:', error);
                    reject(error);
                },
            };

            wx.request(requestOptions);
        });
    }
})
