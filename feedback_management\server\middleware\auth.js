/**
 * 认证中间件
 * 提供统一的用户认证功能，支持session和token两种方式
 */

const ResponseUtil = require('../utils/response');
const logger = require('../utils/logger');

/**
 * 用户认证中间件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
async function requireAuth(req, res, next) {
  try {
    // 首先检查session
    if (req.session.user) {
      logger.debug('用户通过session认证', { userId: req.session.user.id });
      return next();
    }

    // 如果session不存在，检查token
    const token = req.headers.authorization || req.headers.token;
    logger.debug('认证检查', { hasToken: !!token, tokenLength: token ? token.length : 0 });

    if (token) {
      try {
        // 解析token（简单的base64解码）
        const decoded = Buffer.from(token, "base64").toString();
        logger.debug('Token解码结果', { decoded });

        const parts = decoded.split(":");
        const [userId, username] = parts; // 忽略timestamp部分

        logger.debug('Token解析结果', { userId, username, userIdType: typeof userId });

        if (userId && username) {
          logger.debug('用户ID解析', { originalUserId: userId, userIdType: typeof userId });

          // 设置临时用户信息，保持userId为字符串类型
          req.session.user = {
            id: userId,
            username: username,
          };

          // 检查是否有用户信息在header中
          const userInfoHeader = req.headers["x-user-info"];
          if (userInfoHeader) {
            try {
              // 先解码URL编码，再解析JSON
              const decodedUserInfo = decodeURIComponent(userInfoHeader);
              const userInfo = JSON.parse(decodedUserInfo);
              // 更新session中的用户信息
              req.session.user = {
                ...req.session.user,
                PersonId: userInfo.PersonId,
                PersonName: userInfo.PersonName,
                Phone: userInfo.Phone
              };
              logger.debug('解析用户信息成功', { PersonId: userInfo.PersonId, PersonName: userInfo.PersonName });
            } catch (error) {
              logger.warn("解析用户信息失败", error);
              // 如果解码失败，尝试直接解析（兼容性处理）
              try {
                const userInfo = JSON.parse(userInfoHeader);
                req.session.user = {
                  ...req.session.user,
                  PersonId: userInfo.PersonId,
                  PersonName: userInfo.PersonName,
                  Phone: userInfo.Phone
                };
                logger.debug('备用解析用户信息成功', { PersonId: userInfo.PersonId, PersonName: userInfo.PersonName });
              } catch (fallbackError) {
                logger.error("备用解析用户信息也失败", fallbackError);
              }
            }
          }

          // 检查是否有当前公司信息在header中
          const currentCompanyHeader = req.headers["x-current-company"];
          if (currentCompanyHeader) {
            try {
              // 先解码URL编码，再解析JSON
              const decodedHeader = decodeURIComponent(currentCompanyHeader);
              const currentCompany = JSON.parse(decodedHeader);
              req.session.currentCompany = currentCompany;
              logger.debug('解析当前公司信息成功', { companyId: currentCompany.id });
            } catch (error) {
              logger.warn("解析当前公司信息失败", error);
              // 如果解码失败，尝试直接解析（兼容性处理）
              try {
                const currentCompany = JSON.parse(currentCompanyHeader);
                req.session.currentCompany = currentCompany;
                logger.debug('备用解析当前公司信息成功', { companyId: currentCompany.id });
              } catch (fallbackError) {
                logger.error("备用解析也失败", fallbackError);
              }
            }
          }

          // 如果没有当前公司信息，尝试从用户信息中获取默认公司
          if (!req.session.currentCompany) {
            try {
              const User = require('../models/User');
              const user = await User.findById(userId);
              if (user && user.company_id) {
                const Company = require('../models/Company');
                const company = await Company.findById(user.company_id);
                if (company) {
                  req.session.currentCompany = company;
                  logger.debug('设置默认公司信息', { companyId: company.id });
                }
              }
            } catch (error) {
              logger.warn('获取默认公司信息失败', error);
            }
          }

          logger.debug('用户通过token认证', { userId: parseInt(userId) });
          return next();
        }
      } catch (error) {
        logger.warn("Token解析失败", { error: error.message, token: token.substring(0, 20) + '...' });
      }
    }

    // 记录未授权访问
    logger.security('未授权访问尝试', {
      ip: req.ip,
      url: req.url,
      method: req.method,
      userAgent: req.get('User-Agent')
    });

    return ResponseUtil.unauthorized(res, "请先登录");
  } catch (error) {
    logger.error('认证中间件错误', error);
    return ResponseUtil.serverError(res, '认证过程中发生错误');
  }
}





/**
 * 可选的认证中间件
 * 如果有token则解析用户信息，没有也不会阻止请求
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
function optionalAuth(req, res, next) {
  try {
    // 检查session
    if (req.session.user) {
      return next();
    }

    // 检查token
    const token = req.headers.authorization || req.headers.token;
    if (token) {
      try {
        const decoded = Buffer.from(token, "base64").toString();
        const parts = decoded.split(":");
        const [userId, username] = parts;

        if (userId && username) {
          req.session.user = {
            id: parseInt(userId),
            username: username,
          };

          logger.debug('可选认证成功', { userId: parseInt(userId) });
        }
      } catch (error) {
        logger.debug('可选认证token解析失败', { error: error.message });
      }
    }

    next();
  } catch (error) {
    logger.error('可选认证中间件错误', error);
    next(); // 可选认证失败不阻止请求
  }
}

module.exports = {
  requireAuth,
  optionalAuth,
};
