/**
 * 小程序反馈管理系统 API 服务
 * 适用于 IIS + iisnode 部署环境
 */

const express = require('express');
const session = require('express-session');
const cors = require('cors');
const path = require('path');

// 导入配置
const config = require('./config');
const FileManager = require('./utils/file-manager');

// 导入性能监控中间件
const {
  performanceMonitor,
  slowQueryDetector,
  memoryMonitor
} = require('./middleware/performance');

// 导入完整路由
const authRoutes = require('./routes/auth');
const projectRoutes = require('./routes/projects');
const taskRoutes = require('./routes/tasks');
const feedbackRoutes = require('./routes/feedbacks');
const uploadRoutes = require('./routes/upload');
const videoSettingsRoutes = require('./routes/video-settings');


// 创建 Express 应用
const app = express();

// 初始化上传目录结构
FileManager.initializeUploadDirectories();

// 检测运行环境
const isIISNode = process.env.IISNODE_VERSION !== undefined;

// 性能监控中间件（在其他中间件之前）
app.use(performanceMonitor);
app.use(slowQueryDetector(2000)); // 2秒慢查询阈值
app.use(memoryMonitor(500 * 1024 * 1024)); // 500MB内存阈值

// 中间件配置
app.use(cors(config.get('cors')));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Session 配置
app.use(session({
  secret: config.get('session.secret'),
  name: config.get('session.name'),
  resave: config.get('session.resave'),
  saveUninitialized: config.get('session.saveUninitialized'),
  cookie: config.get('session.cookie')
}));

// 静态文件服务 - 提供上传的文件访问
app.use('/upload', express.static('upload', {
  maxAge: '1d', // 缓存1天
  setHeaders: (res, path) => {
    // 设置跨域头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    // 根据文件类型设置Content-Type
    if (path.endsWith('.mp4')) {
      res.setHeader('Content-Type', 'video/mp4');
    } else if (path.endsWith('.webm')) {
      res.setHeader('Content-Type', 'video/webm');
    } else if (path.endsWith('.jpg') || path.endsWith('.jpeg')) {
      res.setHeader('Content-Type', 'image/jpeg');
    } else if (path.endsWith('.png')) {
      res.setHeader('Content-Type', 'image/png');
    } else if (path.endsWith('.webp')) {
      res.setHeader('Content-Type', 'image/webp');
    } else if (path.endsWith('.mp3')) {
      res.setHeader('Content-Type', 'audio/mpeg');
    }
  }
}));


// API 路由注册
app.use('/api/auth', authRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api/tasks', taskRoutes);
app.use('/api/feedbacks', feedbackRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/video-settings', videoSettingsRoutes);
app.use('/api/geocoding', require('./routes/geocoding')); // 地理编码代理路由



// 文件访问API路由
app.get('/api/files/*', (req, res) => {
  const fs = require('fs');
  const path = require('path');
  const mime = require('mime-types');

  try {
    // 从URL中提取文件路径
    const filePath = req.params[0]; // 获取 * 匹配的部分
    const fullPath = path.join(__dirname, 'upload', filePath);

    // 检查文件是否存在
    if (!fs.existsSync(fullPath)) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }

    // 获取文件信息
    const stat = fs.statSync(fullPath);
    const mimeType = mime.lookup(fullPath) || 'application/octet-stream';

    // 设置响应头
    res.setHeader('Content-Type', mimeType);
    res.setHeader('Content-Length', stat.size);
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Cache-Control', 'public, max-age=86400'); // 缓存1天

    // 创建文件流并发送
    const fileStream = fs.createReadStream(fullPath);
    fileStream.pipe(res);

    fileStream.on('error', (error) => {
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: '文件读取失败'
        });
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: '文件访问失败',
      error: error.message
    });
  }
});

// 根路径 - 返回 API 状态信息
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: '反馈管理系统 API 正常工作',
    timestamp: new Date().toISOString(),
    request: {
      method: req.method,
      url: req.url,
      originalUrl: req.originalUrl
    },
    environment: {
      nodeVersion: process.version,
      platform: process.platform,
      isIISNode: isIISNode,
      port: process.env.PORT || 'N/A'
    }
  });
});



// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
    path: req.originalUrl
  });
});

// 错误处理中间件
app.use((error, _req, res, _next) => {
  const status = error.status || 500;
  const message = error.message || '服务器内部错误';

  res.status(status).json({
    success: false,
    message: message,
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  });
});

// 启动服务器
if (isIISNode) {
  // IIS 环境：使用 process.env.PORT (命名管道)
  app.listen(process.env.PORT);
} else {
  // 开发环境：使用指定端口
  const PORT = process.env.PORT || 3000;
  app.listen(PORT);
}

// 错误处理
process.on('uncaughtException', (error) => {
  if (!isIISNode) {
    // 在生产环境中，错误应该通过日志系统记录
  }
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  if (!isIISNode) {
    // 在生产环境中，错误应该通过日志系统记录
  }
  process.exit(1);
});

module.exports = app;
