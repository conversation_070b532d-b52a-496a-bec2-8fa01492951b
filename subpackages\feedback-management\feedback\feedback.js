var app = getApp();
var ImageHelper = require('../utils/image-helper');
var TiandituGeocoding = require('../../../utils/tianditu-geocoding.js');
var LocationPermissionManager = require('../../../utils/location-permission-manager.js');



Page({
  data: {
    currentTask: null,
    currentCompany: null,
    userInfo: null, // 当前用户信息
    tasks: [],
    showTaskSelector: false,
    showCategorySelector: false,
    currentProjectId: null, 
    feedbackForm: {
      feedback_time: "",
      notes: "",
      category: "", // 反馈类别
      longitude: null, // 经度
      latitude: null, // 纬度
      location_desc: "", // 位置描述
      location_status: "unavailable" // 位置状态：authorized, denied, unavailable
    },
    // 地图标记点
    mapMarkers: [],
    // 反馈类别选项
    feedbackCategories: [
      "反馈类别1",
      "反馈类别2",
      "反馈类别3",
      "反馈类别4",
      "反馈类别5",
      "反馈类别6",
      "反馈类别7",
      "反馈类别8",
      "反馈类别9",
      "反馈类别10",
      "反馈类别11",
    ],
    dateTimeRange: [[], [], [], [], []],
    dateTimeValue: [0, 0, 0, 0, 0],
    images: [],
    videos: [],
    audios: [],
    recording: false,
    recordTime: 0,
    recordTimer: null,
    submitting: false,
    // 音频播放相关状态
    currentPlayingIndex: -1, // 当前播放的音频索引
    audioContext: null, // 音频上下文

    playingStates: [], // 每个音频的播放状态
    // 视频时长控制设置
    videoDurationSettings: null,
  },

  onLoad(options) {
    this.checkLogin();
    this.initDateTime();

    this.loadVideoDurationSettings();

    // 获取位置信息（登录时已确认权限）
    this.requestLocation();

    if (options.taskId) {
      this.loadTask(options.taskId);
    } else if (options.projectId) {
      this.setData({ currentProjectId: options.projectId });
      this.loadProjectTasks(options.projectId);
    } else {
      // 没有指定任务单或工程时，提示用户需要先选择任务单
      wx.showToast({
        title: "请先选择任务单",
        icon: "none",
      });
    }
  },

  onUnload() {
    // 清理录音定时器
    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
    }
    // 清理音频播放
    this.stopAllAudio();

    // 页面卸载时不需要清理文件，因为现在文件只在提交时上传
  },

  // 检查登录状态
  checkLogin() {
    const userInfo = wx.getStorageSync("userInfo");
    const currentCompany = wx.getStorageSync("currentCompany");

    if (!userInfo) {
      wx.redirectTo({
        url: "/pages/login/login",
      });
      return;
    }

    if (!currentCompany) {
      wx.showToast({
        title: "请先选择公司",
        icon: "none",
      });
      setTimeout(() => {
        wx.switchTab({
          url: "/pages/index/index",
        });
      }, 2000);
      return;
    }

    // 设置用户信息到data中
    this.setData({
      userInfo,
      currentCompany,
    });
  },

  // 初始化日期时间选择器
  initDateTime() {
    const now = new Date();
    const years = [];
    const months = [];
    const days = [];
    const hours = [];
    const minutes = [];

    // 生成年份 当前年份前后5年
    for (let i = now.getFullYear() - 5; i <= now.getFullYear() + 5; i++) {
      years.push(i + "年");
    }

    // 生成月份
    for (let i = 1; i <= 12; i++) {
      months.push(i + "月");
    }

    // 生成日期
    for (let i = 1; i <= 31; i++) {
      days.push(i + "日");
    }

    // 生成小时
    for (let i = 0; i < 24; i++) {
      hours.push(String(i).padStart(2, "0") + "时");
    }

    // 生成分钟
    for (let i = 0; i < 60; i++) {
      minutes.push(String(i).padStart(2, "0") + "分");
    }

    // 计算当前时间在数组中的正确索引
    const currentYearIndex = 5; // 当前年份在数组中的索引（前后5年，所以当前年份在第6个位置，索引为5）
    const currentMonthIndex = now.getMonth(); // 月份索引（0-11）
    const currentDayIndex = now.getDate() - 1; // 日期索引（0-30，因为数组从1日开始）
    const currentHourIndex = now.getHours(); // 小时索引（0-23）
    const currentMinuteIndex = now.getMinutes(); // 分钟索引（0-59）

    this.setData({
      dateTimeRange: [years, months, days, hours, minutes],
      dateTimeValue: [
        currentYearIndex,
        currentMonthIndex,
        currentDayIndex,
        currentHourIndex,
        currentMinuteIndex,
      ],
    });

    this.updateFeedbackTime();
  },

  // 获取位置信息（权限已在登录时确认）
  async requestLocation() {
    const that = this;

    try {
      // 直接尝试获取位置信息（权限已在登录时确认）
      const locationResult = await LocationPermissionManager.getCurrentLocation();

      if (locationResult.success) {
        // 位置获取成功
        that.setData({
          'feedbackForm.longitude': locationResult.data.longitude,
          'feedbackForm.latitude': locationResult.data.latitude,
          'feedbackForm.location_status': 'authorized'
        });

        // 更新地图标记点
        that.updateMapMarkers(locationResult.data.latitude, locationResult.data.longitude);

        // 使用智能地理编码获取地址
        that.getSmartLocationDesc(locationResult.data.latitude, locationResult.data.longitude);
      } else {
        throw new Error('位置获取失败');
      }
    } catch (error) {
      console.error('获取位置失败:', error);

      // 位置获取失败，设置失败状态
      let errorMsg = '获取位置失败';
      if (error.errorCode === 'PERMISSION_DENIED') {
        errorMsg = '位置权限被拒绝，请在设置中开启位置权限';
        // 显示设置引导
        LocationPermissionManager.showSettingsGuide();
      } else if (error.errorMsg) {
        errorMsg = error.errorMsg;
      }

      that.setData({
        'feedbackForm.location_status': 'denied',
        'feedbackForm.location_desc': errorMsg
      });
    }
  },



  // 使用天地图获取位置描述
  async getSmartLocationDesc(latitude, longitude) {
    const that = this;

    try {
      // 使用天地图进行地理编码
      const result = await TiandituGeocoding.smartGeocode(latitude, longitude, {
        useCache: true,
        showProgress: false
      });

      if (result.success) {
        that.setData({
          'feedbackForm.location_desc': result.address
        });

        wx.showToast({
          title: '位置获取成功',
          icon: 'success',
          duration: 2000
        });

        console.log('位置获取成功:', result.address);
      } else {
        throw new Error('API调用失败');
      }
    } catch (error) {
      console.error('地理编码失败:', error);

      // 降级方案：显示经纬度坐标
      const fallbackDesc = `纬度: ${latitude.toFixed(6)}, 经度: ${longitude.toFixed(6)}`;
      that.setData({
        'feedbackForm.location_desc': fallbackDesc
      });

      wx.showToast({
        title: '位置解析失败，显示坐标',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 更新地图标记点
  updateMapMarkers(latitude, longitude) {
    const markers = [{
      id: 1,
      latitude: latitude,
      longitude: longitude,
      width: 30,
      height: 30,
      title: '反馈位置',
      // 移除callout配置以避免显示空白气泡
      // 使用默认标记图标（确保兼容性）
      iconPath: '',
      anchor: {
        x: 0.5,
        y: 1
      }
    }];

    this.setData({
      mapMarkers: markers
    });
  },

  // 地图点击事件
  onMapTap(e) {
    console.log('地图被点击:', e);

    const { latitude, longitude, location_desc } = this.data.feedbackForm;

    if (latitude && longitude) {
      // 显示详细位置信息
      wx.showModal({
        title: '反馈位置信息',
        content: `位置描述：${location_desc}\n\n经度：${longitude.toFixed(6)}\n纬度：${latitude.toFixed(6)}`,
        confirmText: '确定',
        showCancel: false
      });
    } else {
      wx.showToast({
        title: '位置信息不可用',
        icon: 'none',
        duration: 1500
      });
    }
  },

  // 重新获取位置
  async refreshAccurateLocation() {
    const that = this;

    wx.showLoading({
      title: '获取位置信息...'
    });

    try {
      // 使用位置权限管理器获取位置
      const locationResult = await LocationPermissionManager.getCurrentLocation({
        isHighAccuracy: true,
        highAccuracyExpireTime: 3000
      });

      if (locationResult.success) {
        console.log('重新获取位置成功:', locationResult.data);

        that.setData({
          'feedbackForm.longitude': locationResult.data.longitude,
          'feedbackForm.latitude': locationResult.data.latitude,
          'feedbackForm.location_status': 'authorized'
        });

        // 更新地图标记点
        that.updateMapMarkers(locationResult.data.latitude, locationResult.data.longitude);

        wx.hideLoading();

        // 使用天地图地理编码
        const result = await TiandituGeocoding.smartGeocode(locationResult.data.latitude, locationResult.data.longitude, {
          useCache: false, // 刷新时不使用缓存
          showProgress: true
        });

        if (result.success) {
          that.setData({
            'feedbackForm.location_desc': result.address
          });

          wx.showModal({
            title: '位置更新完成',
            content: '定位成功！\n\n' + result.address,
            showCancel: false,
            confirmText: '好的'
          });
        } else {
          // 降级显示坐标
          const fallbackDesc = `纬度: ${locationResult.data.latitude.toFixed(6)}, 经度: ${locationResult.data.longitude.toFixed(6)}`;
          that.setData({
            'feedbackForm.location_desc': fallbackDesc
          });

          wx.showModal({
            title: '位置更新完成',
            content: '⚠️ 天地图API不可用，显示坐标\n\n' + fallbackDesc,
            showCancel: false,
            confirmText: '好的'
          });
        }
      } else {
        throw new Error('位置获取失败');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('刷新位置失败:', error);

      // 如果是权限问题，显示设置引导
      if (error.errorCode === 'PERMISSION_DENIED') {
        LocationPermissionManager.showSettingsGuide();
      } else {
        wx.showToast({
          title: error.errorMsg || '位置获取失败',
          icon: 'none'
        });
      }
    }
  },







  // 更新反馈时间
  updateFeedbackTime() {
    const { dateTimeRange, dateTimeValue } = this.data;
    const year = parseInt(dateTimeRange[0][dateTimeValue[0]]);
    const month = dateTimeValue[1] + 1;
    const day = dateTimeValue[2] + 1;
    const hour = dateTimeValue[3];
    const minute = dateTimeValue[4];

    // 使用统一的时间格式化方法
    const Formatter = require('../../../utils/formatter');
    const timeStr = Formatter.createTimeString(year, month, day, hour, minute);

    this.setData({
      "feedbackForm.feedback_time": timeStr,
    });
  },

  // 加载任务单
  async loadTask(taskId) {
    try {
      const app = getApp();
      const res = await app.request({
        url: "/api/tasks/" + taskId,
        method: "GET",
      });

      if (res.data.success) {
        const task = Object.assign({}, res.data.data.task, {
          scheduled_time_text: this.formatDateTime(res.data.data.task.scheduled_time)
        });
        this.setData({
          currentTask: task,
          currentProjectId: task.project_id, // 设置当前工程ID
        });

        // 加载当前工程的任务单列表，用于任务单切换，保持当前选中的任务单
        await this.loadProjectTasks(task.project_id, true);
      } else {
        wx.showToast({
          title: res.data.message || "加载任务单失败",
          icon: "none",
        });
      }
    } catch (error) {
      wx.showToast({
        title: "网络错误",
        icon: "none",
      });
    }
  },

  // 加载指定工程的任务单列表
  async loadProjectTasks(projectId, keepCurrentTask = false) {
    try {
      const app = getApp();

      // 获取指定工程的任务单
      const taskRes = await app.request({
        url: "/api/tasks/project/" + projectId,
        method: "GET",
      });

      if (taskRes.data.success && taskRes.data.data.length > 0) {
        const tasks = taskRes.data.data.map((item) => {
          return Object.assign({}, item, {
            scheduled_time_text: this.formatDateTime(item.scheduled_time)
          });
        });

        // 如果需要保持当前任务单，则不覆盖 currentTask
        if (keepCurrentTask && this.data.currentTask) {
          this.setData({
            tasks: tasks,
          });
        } else {
          this.setData({
            tasks: tasks,
            currentTask: tasks[0], // 默认选择第一个任务单
          });
        }
      } else {
        wx.showToast({
          title: "该工程暂无可用任务单",
          icon: "none",
        });
      }
    } catch (error) {
      wx.showToast({
        title: "网络错误",
        icon: "none",
      });
    }
  },

  // 加载所有任务单列表
  async loadAllTasks() {
    try {
      const app = getApp();

      // 直接获取当前公司的所有任务单
      const taskRes = await app.request({
        url: "/api/tasks",
        method: "GET",
      });

      if (taskRes.data.success && taskRes.data.data.length > 0) {
        const tasks = taskRes.data.data.map((item) => {
          return Object.assign({}, item, {
            scheduled_time_text: this.formatDateTime(item.scheduled_time)
          });
        });
        this.setData({
          tasks: tasks,
          currentTask: tasks[0], // 默认选择第一个任务单
        });
      } else {
        wx.showToast({
          title: "暂无可用任务单",
          icon: "none",
        });
      }
    } catch (error) {
      wx.showToast({
        title: "网络错误",
        icon: "none",
      });
    }
  },

  // 加载任务单列表（保留原方法用于兼容）
  async loadTasks() {
    await this.loadAllTasks();
  },

  // 格式化日期时间
  formatDateTime(dateTimeStr) {
    const Formatter = require('../../../utils/formatter');
    return Formatter.formatDateTime(dateTimeStr, 'YYYY-MM-DD HH:mm');
  },

  // 表单输入处理
  onDateTimeChange(e) {
    this.setData({
      dateTimeValue: e.detail.value,
    });
    this.updateFeedbackTime();
  },

  // 输入备注
  onNotesInput(e) {
    this.setData({
      "feedbackForm.notes": e.detail.value,
    });
  },

  // 显示反馈类别选择器
  onShowCategorySelector() {
    this.setData({ showCategorySelector: true });
  },

  // 关闭反馈类别选择器
  onCloseCategorySelector() {
    this.setData({ showCategorySelector: false });
  },

  // 选择反馈类别
  onSelectCategory(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      "feedbackForm.category": category,
      showCategorySelector: false,
    });
  },

  // 打开相机拍摄页面
  onOpenCamera(e) {
    const mode = e.currentTarget.dataset.mode;
    wx.navigateTo({
      url: "../camera-capture/camera-capture?mode=" + mode
    });
  },

  // 相机拍照结果回调 - 接收临时文件信息
  onCameraPhotoResult(result) {
    if (result && result.tempFilePath) {
      // 将临时文件路径添加到images数组中，等待提交时上传
      const images = this.data.images.concat([result.tempFilePath]);
      this.setData({ images });

      wx.showToast({
        title: '照片拍摄成功',
        icon: 'success'
      });
    }
  },

  // 相机录像结果回调 - 接收临时文件信息
  onCameraVideoResult(result) {
    if (result && result.tempFilePath) {
      // 创建视频对象并添加到videos数组中，等待提交时上传
      const videoItem = {
        src: result.tempFilePath,
        duration: result.duration || 0,
        size: result.size || 0,
        fileType: "video"
      };

      // 只允许一个视频，替换现有视频
      this.setData({
        videos: [videoItem]
      });

      wx.showToast({
        title: '视频录制成功',
        icon: 'success'
      });
    }
  },



  // 预览图片
  onPreviewImage(e) {
    const src = e.currentTarget.dataset.src;
    console.log('预览图片:', src);

    wx.previewImage({
      current: src,
      urls: this.data.images.length > 0 ? this.data.images : [src],
      fail: (error) => {
        console.error('预览图片失败:', error);
        wx.showToast({
          title: '预览失败',
          icon: 'none'
        });
      }
    });
  },





  // 构建图片URL
  buildImageUrl(url) {
    const app = getApp();
    return ImageHelper.buildImageUrl(url, app.globalData.baseUrl);
  },

  // 验证图片URL是否可访问
  validateImageUrl(url, filename) {
    console.log(`验证图片URL: ${url} (文件名: ${filename})`);

    try {
      // 简化版本的URL验证
      const isValid = url && (url.startsWith('http') || url.startsWith('/'));
      if (!isValid) {
        console.warn(`图片URL验证失败: ${url}`);
      }

      return isValid;
    } catch (error) {
      console.error(`验证图片URL时出错:`, error);
      return true; // 验证出错时不阻塞流程
    }
  },





  // 测试服务器连接
  testServerConnection(baseUrl) {
    console.log(`测试服务器连接: ${baseUrl}`);

    // 测试基本API连接
    wx.request({
      url: `${baseUrl}/test-upload`,
      method: 'GET',
      timeout: 5000,
      success: (res) => {
        console.log('服务器连接测试成功:', res);
        if (res.statusCode === 200) {
          console.log('服务器API正常工作');
        } else {
          console.warn('服务器返回异常状态码:', res.statusCode);
        }
      },
      fail: (error) => {
        console.error('服务器连接测试失败:', error);
        wx.showModal({
          title: '服务器连接失败',
          content: '无法连接到服务器，请检查网络连接和服务器状态',
          showCancel: false,
          confirmText: '知道了'
        });
      }
    });
  },



  // 录制视频 - 直接跳转到相机页面
  onRecordVideo() {
    // 跳转到相机录制页面
    wx.navigateTo({
      url: '../camera-capture/camera-capture?mode=video'
    });
  },

  // 开始录制视频（备用）
  startVideoRecording(maxDuration) {
    wx.chooseMedia({
      count: 1,
      mediaType: ["video"],
      sourceType: ["camera"],
      maxDuration: maxDuration, // 设置最大录制时长
      camera: 'back', // 默认后置摄像头
      compressed: false, // 不压缩，保持原始质量和尺寸
      success: (res) => {
        const video = res.tempFiles[0];
        this.processVideoFile(video);
      },
      fail: (error) => {
        console.error("录制视频失败:", error);
        wx.showToast({
          title: "录制视频失败",
          icon: "none",
        });
      },
    });
  },



  // 处理视频文件，获取时长信息
  processVideoFile(video) {
    // 获取视频时长
    let duration = 0;
    if (video.duration !== undefined && video.duration !== null && video.duration > 0) {
      duration = Math.round(video.duration);
    }

    // 验证视频时长是否符合设置
    const settings = this.data.videoDurationSettings;
    if (settings && duration > 0) {
      if (duration < settings.min_duration) {
        wx.showModal({
          title: "视频时长不符合要求",
          content: `录制的视频时长为${duration}秒，不能少于${settings.min_duration}秒。请重新录制符合要求的视频。`,
          showCancel: false,
          confirmText: "知道了",
        });
        return;
      }
      if (duration > settings.max_duration) {
        wx.showModal({
          title: "视频时长不符合要求",
          content: `录制的视频时长为${duration}秒，不能超过${settings.max_duration}秒。请重新录制符合要求的视频。`,
          showCancel: false,
          confirmText: "知道了",
        });
        return;
      }
    }

    // 创建视频对象
    const videoItem = {
      src: video.tempFilePath,
      poster: video.thumbTempFilePath,
      duration: duration,
      originalDuration: video.duration, // 保存原始时长用于调试
      size: video.size,
      fileType: video.fileType || "video",
    };

    // 直接添加到列表，不再尝试复杂的获取方法
    const videos = this.data.videos.concat([videoItem]);
    this.setData({ videos });

    // 显示成功提示
    wx.showToast({
      title: "视频录制成功",
      icon: "success",
      duration: 2000,
    });


  },





  // 切换录音状态
  onToggleRecord() {
    if (this.data.recording) {
      this.stopRecord();
    } else {
      this.startRecord();
    }
  },

  // 开始录音
  startRecord() {
    const recorderManager = wx.getRecorderManager();

    recorderManager.start({
      duration: 60000, // 最大60秒
      sampleRate: 16000,
      numberOfChannels: 1,
      encodeBitRate: 96000,
      format: "mp3",
    });

    this.setData({
      recording: true,
      recordTime: 0,
    });

    // 开始计时
    const timer = setInterval(() => {
      const recordTime = this.data.recordTime + 1;
      this.setData({ recordTime });

      if (recordTime >= 60) {
        this.stopRecord();
      }
    }, 1000);

    this.setData({ recordTimer: timer });

    recorderManager.onStop((res) => {
      const audios = this.data.audios.concat([
        {
          tempFilePath: res.tempFilePath,
          duration: Math.round(res.duration / 1000),
          isPlaying: false,
        },
      ]);
      const playingStates = this.data.playingStates.concat([false]);
      this.setData({
        audios,
        playingStates,
      });
    });
  },

  // 停止录音
  stopRecord() {
    const recorderManager = wx.getRecorderManager();
    recorderManager.stop();

    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
    }

    this.setData({
      recording: false,
      recordTime: 0,
      recordTimer: null,
    });
  },

  // 播放音频
  onPlayAudio(e) {
    const index = e.currentTarget.dataset.index;
    const audio = this.data.audios[index];
    const isCurrentlyPlaying = this.data.currentPlayingIndex === index;

    if (isCurrentlyPlaying) {
      // 如果当前音频正在播放，则暂停
      this.pauseAudio(index);
    } else {
      // 停止其他音频播放
      this.stopAllAudio();
      // 播放当前音频
      this.playAudio(index);
    }
  },

  // 播放指定音频
  playAudio(index) {
    const audio = this.data.audios[index];

    const innerAudioContext = wx.createInnerAudioContext();
    innerAudioContext.src = audio.tempFilePath;

    // 设置播放状态
    this.updatePlayingState(index, true);
    this.setData({
      currentPlayingIndex: index,
      audioContext: innerAudioContext,
    });

    // 监听播放结束
    innerAudioContext.onEnded(() => {
      this.updatePlayingState(index, false);
      this.setData({
        currentPlayingIndex: -1,
        audioContext: null,
      });
    });

    // 监听播放错误
    innerAudioContext.onError((error) => {
      console.error("音频播放错误:", error);
      this.updatePlayingState(index, false);
      this.setData({
        currentPlayingIndex: -1,
        audioContext: null,
      });
      wx.showToast({
        title: "播放失败",
        icon: "none",
      });
    });

    innerAudioContext.play();
  },

  // 暂停音频
  pauseAudio(index) {
    if (this.data.audioContext) {
      this.data.audioContext.pause();
    }
    this.updatePlayingState(index, false);
    this.setData({
      currentPlayingIndex: -1,
      audioContext: null,
    });
  },

  // 停止所有音频播放
  stopAllAudio() {
    if (this.data.audioContext) {
      this.data.audioContext.destroy();
    }

    // 重置所有播放状态
    const playingStates = this.data.playingStates.map(() => false);
    this.setData({
      currentPlayingIndex: -1,
      audioContext: null,
      playingStates,
    });
  },

  // 更新播放状态
  updatePlayingState(index, isPlaying) {
    const playingStates = this.data.playingStates.slice();
    playingStates[index] = isPlaying;
    this.setData({ playingStates });
  },



  // 显示任务单选择器
  async onShowTaskSelector() {
    // 只加载当前工程的任务单列表
    if (this.data.currentProjectId) {
      await this.loadProjectTasks(this.data.currentProjectId);
      this.setData({ showTaskSelector: true });
    } else if (this.data.currentTask && this.data.currentTask.project_id) {
      // 如果有当前任务单，使用其工程ID
      await this.loadProjectTasks(this.data.currentTask.project_id);
      this.setData({
        currentProjectId: this.data.currentTask.project_id,
        showTaskSelector: true
      });
    } else {
      wx.showToast({
        title: "无法获取工程信息",
        icon: "none",
      });
    }
  },

  // 关闭任务单选择器
  onCloseTaskSelector() {
    this.setData({ showTaskSelector: false });
  },

  // 选择任务单
  onSelectTask(e) {
    const task = e.currentTarget.dataset.task;
    this.setData({
      currentTask: task,
      currentProjectId: task.project_id, // 更新当前工程ID
      showTaskSelector: false,
    });
  },

  // 停止事件冒泡
  stopPropagation() {},

  // 提交现场信息反馈记录
  async onSubmit() {
    const { currentTask, feedbackForm, images, videos, audios } = this.data;

    if (!currentTask) {
      wx.showToast({
        title: "请先选择任务单",
        icon: "none",
      });
      return;
    }

    if (!feedbackForm.category) {
      wx.showToast({
        title: "请选择反馈类别",
        icon: "none",
      });
      return;
    }

    if (!feedbackForm.feedback_time) {
      wx.showToast({
        title: "请填写反馈时间",
        icon: "none",
      });
      return;
    }

    this.setData({ submitting: true });

    try {
      // 显示上传进度
      const totalFiles = images.length + videos.length + audios.length;
      let uploadedFiles = 0;

      const updateProgress = () => {
        uploadedFiles++;
        const progress = Math.round((uploadedFiles / totalFiles) * 100);
        wx.showLoading({
          title: `上传中 ${progress}%`,
          mask: true
        });
      };

      if (totalFiles > 0) {
        wx.showLoading({
          title: "开始上传文件...",
          mask: true
        });
      }



      // 准备多媒体文件数据
      const mediaFiles = [];
      const uploadErrors = [];

      // 上传所有图片（包括相机拍摄和相册选择的）
      for (let i = 0; i < images.length; i++) {
        try {
          // 检查文件大小
          const fileInfo = await this.getFileInfo(images[i]);
          if (fileInfo.size > 10 * 1024 * 1024) { // 10MB
            console.warn(`图片文件过大 (${(fileInfo.size / 1024 / 1024).toFixed(2)}MB)`);
          }

          const uploadRes = await this.uploadFile(images[i], "image");
          if (uploadRes) {
            mediaFiles.push({
              file_type: "image",
              file_name: uploadRes.filename,
              file_path: uploadRes.url,
              file_size: uploadRes.size,
            });
          }
          updateProgress();
        } catch (error) {
          console.error(`图片上传失败:`, error.message || error.errMsg);
          let errorMsg = error.message || error.errMsg || "未知错误";

          // 将技术错误转换为用户友好的错误信息
          if (errorMsg.includes("ECONNRESET")) {
            errorMsg = "网络连接中断，请检查网络后重试";
          } else if (errorMsg.includes("timeout")) {
            errorMsg = "上传超时，请检查网络或稍后重试";
          } else if (errorMsg.includes("ENOTFOUND")) {
            errorMsg = "无法连接到服务器，请检查网络设置";
          }

          uploadErrors.push(`图片 ${i + 1}: ${errorMsg}`);
        }
      }

      // 上传所有视频（包括相机录制和相册选择的）
      for (let i = 0; i < videos.length; i++) {
        try {
          // 检查视频文件大小
          const fileInfo = await this.getFileInfo(videos[i].src);
          if (fileInfo.size > 50 * 1024 * 1024) { // 50MB
            console.warn(`视频文件过大 (${(fileInfo.size / 1024 / 1024).toFixed(2)}MB)`);
          }

          const uploadRes = await this.uploadFile(videos[i].src, "video");
          if (uploadRes) {
            mediaFiles.push({
              file_type: "video",
              file_name: uploadRes.filename,
              file_path: uploadRes.url,
              file_size: uploadRes.size,
              duration: videos[i].duration,
            });
          }
          updateProgress();
        } catch (error) {
          console.error(`视频上传失败:`, error.message || error.errMsg);
          let errorMsg = error.message || error.errMsg || "未知错误";

          // 将技术错误转换为用户友好的错误信息
          if (errorMsg.includes("ECONNRESET")) {
            errorMsg = "网络连接中断，请检查网络后重试";
          } else if (errorMsg.includes("timeout")) {
            errorMsg = "上传超时，文件可能过大，建议压缩后重试";
          } else if (errorMsg.includes("ENOTFOUND")) {
            errorMsg = "无法连接到服务器，请检查网络设置";
          }

          uploadErrors.push(`视频 ${i + 1}: ${errorMsg}`);
        }
      }

      // 上传音频
      for (let i = 0; i < audios.length; i++) {
        try {

          const uploadRes = await this.uploadFile(audios[i].tempFilePath, "audio");
          if (uploadRes) {
            mediaFiles.push({
              file_type: "audio",
              file_name: uploadRes.filename,
              file_path: uploadRes.url,
              file_size: uploadRes.size,
              duration: audios[i].duration,
            });
          }
          updateProgress();
        } catch (error) {
          console.error(`音频上传失败:`, error.message || error.errMsg);
          uploadErrors.push(`音频 ${i + 1}: ${error.message || error.errMsg}`);
        }
      }

      // 如果有上传错误，显示警告但继续提交
      if (uploadErrors.length > 0) {
        console.warn("部分文件上传失败");
        wx.hideLoading();

        const result = await new Promise((resolve) => {
          wx.showModal({
            title: "部分文件上传失败",
            content: `${uploadErrors.length}个文件上传失败，是否继续提交？\n\n失败文件：\n${uploadErrors.slice(0, 3).join('\n')}${uploadErrors.length > 3 ? '\n...' : ''}`,
            confirmText: "继续提交",
            cancelText: "取消",
            success: (res) => resolve(res.confirm)
          });
        });

        if (!result) {
          this.setData({ submitting: false });
          return;
        }
      } else if (totalFiles > 0) {
        wx.hideLoading();
      }

      // 提交现场信息反馈记录
      wx.showLoading({
        title: "提交中...",
        mask: true
      });

      const app = getApp();


      const res = await app.request({
        url: "/api/feedbacks",
        method: "POST",
        data: Object.assign({
          task_number: currentTask.id, // 修复字段名：使用task_number而不是task_id
          media_files: mediaFiles,
        }, feedbackForm),
      });

      wx.hideLoading();

      if (res.data.success) {
        // 标记为已提交，防止页面卸载时清理文件
        this.setData({ submitting: true });

        wx.showToast({
          title: "提交成功",
          icon: "success",
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        console.error("服务器返回错误:", res.data);
        wx.showModal({
          title: "提交失败",
          content: res.data.message || "服务器返回未知错误",
          showCancel: false
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error("提交现场信息反馈记录失败:", error);

      let errorMessage = "网络错误";
      if (error.message) {
        errorMessage = error.message;
      } else if (error.errMsg) {
        if (error.errMsg.includes("timeout")) {
          errorMessage = "请求超时，请检查网络连接";
        } else if (error.errMsg.includes("fail")) {
          errorMessage = "网络连接失败，请重试";
        } else {
          errorMessage = error.errMsg;
        }
      }

      wx.showModal({
        title: "提交失败",
        content: errorMessage,
        showCancel: false
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 加载视频时长设置
  async loadVideoDurationSettings() {
    try {
      const app = getApp();
      const res = await app.request({
        url: "/api/video-settings",
        method: "GET",
      });

      if (res.data && res.data.success) {
        this.setData({
          videoDurationSettings: res.data.data,
        });

      }
    } catch (error) {
      console.error("加载视频时长设置失败:", error);
      // 设置默认值
      this.setData({
        videoDurationSettings: {
          min_duration: 5,
          max_duration: 300,
        },
      });
    }
  },

  // 显示上传提示
  showUploadTips() {
    wx.showModal({
      title: "上传提示",
      content: "为提高上传成功率，建议：\n\n1. 使用稳定的WiFi网络\n2. 单个文件不超过10MB\n3. 视频建议压缩后上传\n4. 避免在网络信号弱的地方上传\n5. 上传过程中不要切换网络",
      confirmText: "知道了",
      showCancel: false
    });
  },



  // 获取文件信息
  async getFileInfo(filePath) {
    return new Promise((resolve, reject) => {
      wx.getFileInfo({
        filePath: filePath,
        success: (res) => {
          resolve({
            size: res.size,
            digest: res.digest
          });
        },
        fail: (error) => {
          console.warn("获取文件信息失败:", error);
          // 如果获取失败，返回默认值
          resolve({ size: 0 });
        }
      });
    });
  },

  // 上传文件（带重试机制）
  async uploadFile(filePath, type, retryCount) {
    if (retryCount === undefined) {
      retryCount = 0;
    }
    const maxRetries = 5; // 增加重试次数到5次
    const baseRetryDelay = 2000; // 增加基础延迟到2秒

    return new Promise((resolve, reject) => {
      const app = getApp();
      const authToken = wx.getStorageSync("authToken");
      const currentCompany = wx.getStorageSync("currentCompany");
      const userInfo = wx.getStorageSync("userInfo");

      // 构建请求头
      const header = {};
      if (authToken) {
        header.Authorization = authToken;
      }
      if (userInfo) {
        try {
          const userInfoJson = JSON.stringify({
            PersonId: userInfo.PersonId || userInfo.userId,
            PersonName: userInfo.PersonName,
            Phone: userInfo.Phone
          });
          header["X-User-Info"] = encodeURIComponent(userInfoJson);
        } catch (error) {
          console.warn('序列化用户信息失败:', error);
        }
      }
      if (currentCompany) {
        const companyJson = JSON.stringify(currentCompany);
        header["X-Current-Company"] = encodeURIComponent(companyJson);
      }

      // 检查网络状态
      wx.getNetworkType({
        success: (networkRes) => {
          if (networkRes.networkType === 'none') {
            reject(new Error('网络连接不可用，请检查网络设置'));
            return;
          }
        }
      });

      const uploadTask = wx.uploadFile({
        url: `${app.globalData.baseUrl}/api/upload/single`,
        filePath: filePath,
        name: "file",
        header: header,
        timeout: 90000, // 增加超时时间到90秒
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.success) {
              resolve(data.data);
            } else {
              console.error("上传失败:", data.message);
              reject(new Error(data.message));
            }
          } catch (error) {
            console.error("解析上传响应失败:", error.message);
            reject(error);
          }
        },
        fail: (error) => {
          console.error(`上传失败:`, error.errMsg || error.message);

          // 检查是否是网络错误且还有重试次数
          if (retryCount < maxRetries &&
              (error.errMsg.includes("ECONNRESET") ||
               error.errMsg.includes("timeout") ||
               error.errMsg.includes("network") ||
               error.errMsg.includes("ENOTFOUND") ||
               error.errMsg.includes("ETIMEDOUT") ||
               error.errMsg.includes("fail"))) {

            // 使用指数退避算法计算延迟时间
            const retryDelay = baseRetryDelay * Math.pow(2, retryCount) + Math.random() * 1000;


            setTimeout(async () => {
              try {
                const result = await this.uploadFile(filePath, type, retryCount + 1);
                resolve(result);
              } catch (retryError) {
                reject(retryError);
              }
            }, retryDelay);
          } else {
            reject(error);
          }
        },
      });

      // 监听上传进度
      uploadTask.onProgressUpdate((res) => {
        // 可以在这里添加进度显示逻辑
      });
    });
  },


});
