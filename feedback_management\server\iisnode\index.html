<html>
<head>
    <title>iisnode logs</title>
    <style type="text/css">
        body
        {
            font-family: "Trebuchet MS" , Arial, Helvetica, sans-serif;
        }
        table
        {
            border-collapse: collapse;
        }
        td, th
        {
            border: 1px solid lightgray;
            padding: 3px 7px 2px 7px;
        }
        th
        {
            text-align: left;
            padding-top: 5px;
            padding-bottom: 4px;
            background-color: Gray;
            color: #ffffff;
        }
        td.stderr
        {
            color: Red;
        }
    </style>
</head>
<body>
    <table id="logFilesTable">
        <tr>
            <th>
                Computer name
            </th>
            <th>
                PID
            </th>
            <th>
                Type
            </th>
            <th>
                Created
            </th>
            <th>
                Link
            </th>
        </tr>
    </table>
    <p id="lastUpdated"></p>
    <script type="text/javascript">

        // this is replaced with actual data at runtime by code in interceptor.js
        var logFiles = [{"file":"LAPTOP-41PESEN1-12756-stderr-1753940318518.txt","computername":"LAPTOP-41PESEN1","pid":12756,"type":"stderr","created":1753940318518},{"file":"LAPTOP-41PESEN1-12756-stdout-1753940318516.txt","computername":"LAPTOP-41PESEN1","pid":12756,"type":"stdout","created":1753940318516},{"file":"LAPTOP-41PESEN1-22136-stderr-1753840365026.txt","computername":"LAPTOP-41PESEN1","pid":22136,"type":"stderr","created":1753840365026},{"file":"LAPTOP-41PESEN1-22136-stdout-1753840365023.txt","computername":"LAPTOP-41PESEN1","pid":22136,"type":"stdout","created":1753840365023},{"file":"LAPTOP-41PESEN1-2388-stderr-1753932431990.txt","computername":"LAPTOP-41PESEN1","pid":2388,"type":"stderr","created":1753932431990},{"file":"LAPTOP-41PESEN1-2388-stdout-1753932431988.txt","computername":"LAPTOP-41PESEN1","pid":2388,"type":"stdout","created":1753932431988},{"file":"LAPTOP-41PESEN1-24072-stderr-1753771851514.txt","computername":"LAPTOP-41PESEN1","pid":24072,"type":"stderr","created":1753771851514},{"file":"LAPTOP-41PESEN1-24072-stdout-1753771851512.txt","computername":"LAPTOP-41PESEN1","pid":24072,"type":"stdout","created":1753771851512},{"file":"LAPTOP-41PESEN1-25756-stderr-1753843291549.txt","computername":"LAPTOP-41PESEN1","pid":25756,"type":"stderr","created":1753843291549},{"file":"LAPTOP-41PESEN1-25756-stdout-1753843291547.txt","computername":"LAPTOP-41PESEN1","pid":25756,"type":"stdout","created":1753843291547},{"file":"LAPTOP-41PESEN1-30748-stderr-1753749415103.txt","computername":"LAPTOP-41PESEN1","pid":30748,"type":"stderr","created":1753749415103},{"file":"LAPTOP-41PESEN1-31980-stderr-1753861878644.txt","computername":"LAPTOP-41PESEN1","pid":31980,"type":"stderr","created":1753861878644},{"file":"LAPTOP-41PESEN1-31980-stdout-1753861878637.txt","computername":"LAPTOP-41PESEN1","pid":31980,"type":"stdout","created":1753861878637},{"file":"LAPTOP-41PESEN1-32532-stderr-1753940653126.txt","computername":"LAPTOP-41PESEN1","pid":32532,"type":"stderr","created":1753940653126},{"file":"LAPTOP-41PESEN1-32532-stdout-1753940653124.txt","computername":"LAPTOP-41PESEN1","pid":32532,"type":"stdout","created":1753940653124},{"file":"LAPTOP-41PESEN1-32852-stderr-1753940402023.txt","computername":"LAPTOP-41PESEN1","pid":32852,"type":"stderr","created":1753940402023},{"file":"LAPTOP-41PESEN1-32852-stdout-1753940402020.txt","computername":"LAPTOP-41PESEN1","pid":32852,"type":"stdout","created":1753940402020},{"file":"LAPTOP-41PESEN1-4972-stderr-1753771749763.txt","computername":"LAPTOP-41PESEN1","pid":4972,"type":"stderr","created":1753771749763},{"file":"LAPTOP-41PESEN1-4972-stdout-1753771749762.txt","computername":"LAPTOP-41PESEN1","pid":4972,"type":"stdout","created":1753771749762},{"file":"LAPTOP-41PESEN1-7584-stdout-1753771490013.txt","computername":"LAPTOP-41PESEN1","pid":7584,"type":"stdout","created":1753771490013},{"file":"LAPTOP-41PESEN1-9684-stderr-1753923341033.txt","computername":"LAPTOP-41PESEN1","pid":9684,"type":"stderr","created":1753923341033},{"file":"LAPTOP-41PESEN1-9684-stdout-1753923341028.txt","computername":"LAPTOP-41PESEN1","pid":9684,"type":"stdout","created":1753923341028}];
        var lastUpdated = 1753940653913;
        var date = new Date();

        date.setTime(lastUpdated);
        document.getElementById('lastUpdated').innerHTML = 'Index was last updated ' + date;

        logFiles.sort(function (a, b) {
            return b.created - a.created;
        });

        var logFilesTable = document.getElementById("logFilesTable");
        for (var i = 0; i < logFiles.length; i++) {
            var logFile = logFiles[i];
            date.setTime(logFile.created);
            var row = logFilesTable.insertRow(-1);
            var computerNameCell = row.insertCell(0);
            var pidCell = row.insertCell(1);
            var typeCell = row.insertCell(2);
            var dateCell = row.insertCell(3);
            var logCell = row.insertCell(4);
            computerNameCell.innerHTML = logFile.computername;
            pidCell.innerHTML = logFile.pid.toString();
            typeCell.innerHTML = logFile.type;
            typeCell.setAttribute('class', logFile.type);
            dateCell.innerHTML = date.toString();
            logCell.innerHTML = '<a href="' + logFile.file + '">log</a>';
        };

    </script>
</body>
</html>
